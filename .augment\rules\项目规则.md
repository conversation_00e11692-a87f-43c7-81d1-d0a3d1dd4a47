---
type: "always_apply"
---

# WinForms 工业通用架构项目规则

## 1. 项目结构规则

### 1.1 解决方案结构
```
IndustrialHMI/
├── Shell/                      # 主程序
├── Contracts/                  # 共享接口
├── Services/                   # 核心服务
├── Modules/                    # 运行时模块目录
├── Modules.Sources/            # 模块源代码
├── Tests/                      # 测试项目
├── Development_Documents/      # 开发文档
└── IndustrialHMI.sln
```

### 1.2 模块项目结构
```
ModuleName/
├── ModuleNameModule.cs         # 模块主类（必须）
├── Views/                      # 视图层
│   ├── ModuleNameView.cs
│   └── ModuleNameView.Designer.cs
├── Presenters/                 # 表示器层
│   └── ModuleNamePresenter.cs
├── Models/                     # 数据模型
├── Services/                   # 业务服务
└── ModuleName.csproj
```

## 2. 代码规范

### 2.1 命名规范
- **模块类**: `{功能名}Module` (如: DeviceModule)
- **视图类**: `{功能名}View` (如: DeviceView)
- **表示器**: `{功能名}Presenter` (如: DevicePresenter)
- **事件类**: `{业务}Event` (如: DeviceConnectionEvent)
- **服务接口**: `I{服务名}Service` (如: IDeviceService)
- **事件处理**: `On{事件名}` (如: OnDeviceConnectionChanged)

### 2.2 文件命名
- 类文件与类名保持一致
- 接口文件以I开头
- 事件文件放在 `Contracts/Events/` 目录
- 服务接口放在 `Contracts/Services/` 目录

### 2.3 代码注释
```csharp
/// <summary>
/// 模块功能描述
/// </summary>
/// <remarks>
/// 详细说明和注意事项
/// </remarks>
public class SampleModule : IModule
{
    /// <summary>
    /// 属性功能描述
    /// </summary>
    /// <remarks>由ModuleLoader注入</remarks>
    public IEventAggregator EventAggregator { get; set; }
}
```

## 3. 开发流程规则

### 3.1 模块开发流程
1. **需求分析** - 明确模块功能和接口
2. **接口设计** - 定义事件和服务接口
3. **MVP实现** - 按照模板实现三层结构
4. **单元测试** - 编写测试用例
5. **集成测试** - 验证模块间通信
6. **代码审查** - 团队审查代码质量
7. **文档更新** - 更新相关文档

### 3.2 代码提交规则
- **提交信息格式**: `[模块名] 功能描述`
- **示例**: `[DeviceModule] 添加设备连接状态监控功能`
- **每次提交必须**: 编译通过 + 测试通过 + 代码审查通过

### 3.3 分支管理
- `main` - 主分支，稳定版本
- `develop` - 开发分支，集成测试
- `feature/{模块名}` - 功能分支，单个模块开发
- `hotfix/{问题描述}` - 紧急修复分支

## 4. 质量控制规则

### 4.1 代码质量标准
- **编译**: 无警告，无错误
- **测试覆盖率**: ≥ 80%
- **代码审查**: 必须通过同行审查
- **性能**: 模块加载时间 < 2秒，UI响应 < 100ms
- **内存**: 长期运行无内存泄漏

### 4.2 必须检查项
- [ ] 实现IModule接口
- [ ] 有无参构造函数
- [ ] 正确的依赖注入验证
- [ ] 事件订阅设置keepSubscriberReferenceAlive: false
- [ ] 实现Dispose方法并取消订阅
- [ ] UI更新使用SafeUpdateUI方法
- [ ] 异常处理不影响其他模块
- [ ] 添加必要的日志记录

### 4.3 禁止项检查
- [ ] 无模块间直接引用
- [ ] 无构造函数中的复杂逻辑
- [ ] 无事件处理中的未捕获异常
- [ ] 无UI线程阻塞操作
- [ ] 无遗漏的事件订阅取消

## 5. 测试规则

### 5.1 单元测试要求
- 每个模块必须有对应的测试项目
- 测试项目命名: `{模块名}.Tests`
- 测试类命名: `{类名}Tests`
- 测试方法命名: `{方法名}_{场景}_{期望结果}`

### 5.2 测试覆盖范围
- 模块初始化和释放
- 事件发布和订阅
- 异常处理逻辑
- MVP交互逻辑
- 业务逻辑验证

### 5.3 集成测试要求
- 模块加载测试
- 模块间通信测试
- 系统启动关闭测试
- 长期运行稳定性测试

## 6. 部署规则

### 6.1 构建规则
- 使用统一的构建脚本
- 输出目录结构标准化
- 自动复制依赖文件
- 生成版本信息文件

### 6.2 发布规则
- 版本号格式: `主版本.次版本.修订版本.构建号`
- 发布包命名: `IndustrialHMI_v{版本号}_{日期}.zip`
- 必须包含: 可执行文件、配置文件、文档、示例模块

### 6.3 部署检查
- [ ] 所有DLL文件完整
- [ ] 配置文件正确
- [ ] 日志目录可写
- [ ] .NET Framework版本兼容
- [ ] 目标环境测试通过

## 7. 文档规则

### 7.1 必需文档
- 模块功能说明文档
- API接口文档
- 部署配置文档
- 用户操作手册

### 7.2 文档更新规则
- 代码变更必须同步更新文档
- 文档版本与代码版本保持一致
- 重要变更必须更新变更日志

## 8. 安全规则

### 8.1 代码安全
- 不在代码中硬编码敏感信息
- 输入验证和参数检查
- 异常信息不泄露敏感数据
- 使用安全的序列化方式

### 8.2 模块安全
- 模块DLL签名验证（可选）
- 模块权限控制
- 恶意模块检测机制

## 9. 性能规则

### 9.1 性能指标
- 应用启动时间: < 10秒
- 模块加载时间: < 2秒/模块
- UI响应时间: < 100ms
- 内存使用: 稳定在合理范围
- CPU使用率: 正常运行 < 20%

### 9.2 性能优化要求
- 使用异步操作处理耗时任务
- 合理使用缓存机制
- 及时释放不需要的资源
- 避免频繁的UI更新

## 10. 维护规则

### 10.1 日志规则
- 使用统一的日志框架(Serilog)
- 日志级别: Debug, Info, Warn, Error
- 关键操作必须记录日志
- 日志文件大小和数量控制

### 10.2 监控规则
- 系统性能监控
- 模块状态监控
- 异常统计和报告
- 用户操作审计

**请严格遵守以上开发规则**
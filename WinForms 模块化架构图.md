WinForms 模块化架构图，展示 DryIoc + EventAggregator + MEF 如何配合。

---

## WinForms 模块化架构（DryIoc + EventAggregator + MEF）

```
┌─────────────────────────────────────────────┐
│               主程序（Shell）               │
│  ┌───────────────────────────────────────┐  │
│  │   Program.cs  MainForm.cs             │  │
│  │   - 初始化 DryIoc 容器                  │  │
│  │   - 初始化 EventAggregator              │  │
│  │   - 调用 MEF 扫描插件目录               │  │
│  │   - 注册模块到 DryIoc 容器              │  │
│  │   - 加载 UI 模块（UserControl）         │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────┐
│             模块（Module1.dll）              │
│  ┌───────────────────────────────────────┐  │
│  │ [Export(typeof(IModule))]              │  │
│  │ public class Module1  IModule         │  │
│  │ {                                      │  │
│  │   ctor(EventAggregator, ServiceA)      │  │
│  │   注册事件订阅                          │  │
│  │   提供 UI 控件（UserControl1）          │  │
│  │ }                                      │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────┐
│             模块（Module2.dll）              │
│  ┌───────────────────────────────────────┐  │
│  │ [Export(typeof(IModule))]              │  │
│  │ public class Module2  IModule         │  │
│  │ {                                      │  │
│  │   发布事件 (ea.Publish(...))            │  │
│  │   调用 DryIoc 注入的服务                 │  │
│  │ }                                      │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘

```

---

### 工作流程

1. DryIoc

    作为 IoC 容器，负责对象的构造和依赖注入（包括模块、服务、EventAggregator 实例）。
2. EventAggregator

    作为事件总线，让不同模块之间发布订阅事件而不直接引用对方。
3. MEF

    在运行时扫描插件目录（比如 `Modules` 文件夹），加载带 `[Export]` 特性的类（模块）。
    把这些模块对象交给 DryIoc 管理，并注入依赖（包括 EventAggregator）。
4. UI加载

    主程序通过接口（IModule）调用模块的 UI 部分，把 UserControl 加到主窗体。
5. 模块通信

    如果 Module2 想通知 Module1，只需要 `ea.Publish(new MyEvent(...))`，Module1 提前订阅了这个事件就能收到。

---

### 优点

 模块可热插拔（添加删除 DLL 不改主程序）。
 模块之间完全解耦（只依赖接口和事件）。
 主程序不需要知道模块内部细节。
 容器统一管理生命周期，避免内存泄漏。

---


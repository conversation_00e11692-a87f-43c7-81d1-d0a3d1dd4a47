# WinForms 工业上位机模块化框架技术实现文档

## 1. 架构概述

### 1.1 设计目标
本技术文档详细描述了基于 **DryIoc + EventAggregator + MEF** 的 WinForms 模块化架构实现方案，旨在构建一个高度解耦、鲁棒性强、易于扩展的工业上位机软件框架，支持"搭积木式"快速开发工业 HMI 系统。

### 1.2 核心架构
```
┌─────────────────────────────────────────────┐
│               主程序（Shell）               │
│  ┌───────────────────────────────────────┐  │
│  │   Program.cs  MainForm.cs             │  │
│  │   - 初始化 DryIoc 容器                  │  │
│  │   - 初始化 EventAggregator              │  │
│  │   - 调用 MEF 扫描插件目录               │  │
│  │   - 注册模块到 DryIoc 容器              │  │
│  │   - 加载 UI 模块（UserControl）         │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────┐
│             模块（Module1.dll）              │
│  ┌───────────────────────────────────────┐  │
│  │ [Export(typeof(IModule))]              │  │
│  │ public class Module1 : IModule        │  │
│  │ {                                      │  │
│  │   ctor(EventAggregator, ServiceA)      │  │
│  │   注册事件订阅                          │  │
│  │   提供 UI 控件（UserControl1）          │  │
│  │ }                                      │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────┐
│             模块（Module2.dll）              │
│  ┌───────────────────────────────────────┐  │
│  │ [Export(typeof(IModule))]              │  │
│  │ public class Module2 : IModule        │  │
│  │ {                                      │  │
│  │   发布事件 (ea.Publish(...))            │  │
│  │   调用 DryIoc 注入的服务                 │  │
│  │ }                                      │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
```

### 1.3 核心组件职责
| 组件 | 职责 |
|------|------|
| **DryIoc** | 作为 IoC 容器，负责对象的构造和依赖注入（包括模块、服务、EventAggregator 实例） |
| **EventAggregator** | 作为事件总线，让不同模块之间发布订阅事件而不直接引用对方 |
| **MEF** | 在运行时扫描插件目录（如 `Modules` 文件夹），加载带 `[Export]` 特性的类（模块） |

## 2. 环境配置

### 2.1 开发环境
- **操作系统**: Windows 10/11 (64位)
- **开发工具**: VSCode 1.80+ 或 Visual Studio 2022
- **.NET Framework**: 4.8+
- **必备扩展**:
  - C# for Visual Studio Code (powered by OmniSharp)
  - C# Dev Kit
  - NuGet Package Manager

### 2.2 项目结构
```
IndustrialHMI/
├── .vscode/                    # VSCode 配置
├── Shell/                      # 主程序
├── Contracts/                  # 共享接口
├── Services/                   # 核心服务
├── Modules/                    # 运行时模块目录
├── Modules.Sources/            # 模块源代码
├── build/                      # 构建脚本
└── IndustrialHMI.sln           # 解决方案文件
```

### 2.3 必需 NuGet 包
| 项目 | 包 | 说明 |
|------|-----|------|
| 所有项目 | DryIoc | 依赖注入容器 |
| Shell | Prism.Core | 提供 EventAggregator 实现 |
| 所有项目 | System.ComponentModel.Composition | MEF 支持 |
| Services | NLog 或 Serilog | 日志记录 |

## 3. 核心组件实现

### 3.1 DryIoc 容器初始化

**文件**: `Shell/Bootstrapper/ContainerBootstrapper.cs`

```csharp
using DryIoc;
using Prism.Events;

namespace Shell.Bootstrapper
{
    public static class ContainerBootstrapper
    {
        public static IContainer CreateContainer()
        {
            var container = new Container(rules => rules
                .With(propertiesAndFields: PropertiesAndFields.Properties)
                .With(FactoryMethod.ConstructorWithResolvableArguments));
                
            // 注册事件聚合器
            container.Register<IEventAggregator, EventAggregator>(Reuse.Singleton);
            
            // 注册核心服务
            container.Register<ILogger, NLogLogger>(Reuse.Singleton);
            container.Register<IConfigurationService, ConfigurationService>(Reuse.Singleton);
            container.Register<ICommunicationService, ModbusService>(Reuse.Scoped);
            
            // 注册MVP基础组件
            container.Register<IView, MainForm>(ifAlreadyRegistered: IfAlreadyRegistered.Replace);
            
            return container;
        }
    }
}
```

### 3.2 EventAggregator 事件总线

**文件**: `Contracts/Events/CustomEvents.cs`

```csharp
using Prism.Events;

namespace Contracts.Events
{
    // 通用系统事件
    public class SystemStartupEvent : PubSubEvent { }
    public class SystemShutdownEvent : PubSubEvent { }
    
    // 设备连接事件
    public class DeviceConnectionEvent : PubSubEvent<DeviceConnectionStatus> { }
    
    public class DeviceConnectionStatus
    {
        public string DeviceId { get; set; }
        public bool IsConnected { get; set; }
        public DateTime Timestamp { get; set; }
        public string ErrorMessage { get; set; }
    }
    
    // 报警事件
    public class AlarmEvent : PubSubEvent<AlarmInfo> { }
    
    public class AlarmInfo
    {
        public string AlarmId { get; set; }
        public string Message { get; set; }
        public AlarmLevel Level { get; set; }
        public DateTime TriggerTime { get; set; }
        public bool IsAcknowledged { get; set; }
        public string Source { get; set; }
    }
    
    public enum AlarmLevel
    {
        Info,
        Warning,
        Error,
        Critical
    }
    
    // 实时数据事件
    public class RealTimeDataEvent : PubSubEvent<Dictionary<string, object>> { }
}
```

### 3.3 MEF 模块加载器

**文件**: `Shell/Bootstrapper/ModuleLoader.cs`

```csharp
using System.ComponentModel.Composition;
using System.ComponentModel.Composition.Hosting;
using System.ComponentModel.Composition.Primitives;
using Contracts;
using DryIoc;

namespace Shell.Bootstrapper
{
    public class ModuleLoader
    {
        private readonly IContainer _container;
        private readonly ILogger _logger;
        private readonly List<IModule> _loadedModules = new List<IModule>();

        public ModuleLoader(IContainer container, ILogger logger)
        {
            _container = container;
            _logger = logger;
        }

        public IReadOnlyList<IModule> LoadModules(string modulesPath)
        {
            _logger.Info($"开始加载模块，路径: {modulesPath}");
            
            // 确保目录存在
            if (!Directory.Exists(modulesPath))
            {
                Directory.CreateDirectory(modulesPath);
                _logger.Warn($"模块目录不存在，已创建: {modulesPath}");
                return _loadedModules;
            }

            try
            {
                // 创建目录目录
                var catalog = new DirectoryCatalog(modulesPath, "*.dll");
                var container = new CompositionContainer(catalog);
                
                // 获取所有模块
                var modules = container.GetExports<IModule>();
                
                _logger.Info($"发现 {modules.Count()} 个模块");
                
                foreach (var moduleExport in modules)
                {
                    try
                    {
                        var module = moduleExport.Value;
                        
                        // 使用 DryIoc 解析依赖
                        ResolveModuleDependencies(module);
                        
                        // 初始化模块
                        module.Initialize();
                        
                        _loadedModules.Add(module);
                        _logger.Info($"成功加载模块: {module.Name}");
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"加载模块失败: {ex.Message}", ex);
                        // 模块加载失败不影响其他模块
                    }
                }
            }
            catch (ReflectionTypeLoadException ex)
            {
                _logger.Error($"模块加载异常: {ex.Message}");
                foreach (var loaderEx in ex.LoaderExceptions)
                {
                    _logger.Error($"加载器异常: {loaderEx.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"模块加载过程中发生错误: {ex.Message}", ex);
            }

            return _loadedModules;
        }

        private void ResolveModuleDependencies(IModule module)
        {
            // 获取模块类型
            var moduleType = module.GetType();
            
            // 获取所有属性
            var properties = moduleType.GetProperties()
                .Where(p => p.CanWrite && p.GetSetMethod() != null);
                
            // 注入属性依赖
            foreach (var property in properties)
            {
                try
                {
                    if (_container.CanResolve(property.PropertyType))
                    {
                        var value = _container.Resolve(property.PropertyType);
                        property.SetValue(module, value);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warn($"无法注入属性 {property.Name}: {ex.Message}");
                }
            }
            
            // 检查构造函数是否需要注入
            var constructors = moduleType.GetConstructors();
            if (constructors.Any())
            {
                var constructor = constructors.First();
                var parameters = constructor.GetParameters();
                
                if (parameters.Any())
                {
                    var args = parameters.Select(p => 
                        _container.CanResolve(p.ParameterType) ? 
                        _container.Resolve(p.ParameterType) : 
                        null)
                        .ToArray();
                        
                    // 重新创建模块实例（如果使用MEF默认构造）
                    if (args.All(a => a != null))
                    {
                        var newModule = Activator.CreateInstance(moduleType, args);
                        // 替换当前module
                        var index = _loadedModules.IndexOf(module);
                        if (index >= 0)
                        {
                            _loadedModules[index] = (IModule)newModule;
                        }
                    }
                }
            }
        }

        public void UnloadModules()
        {
            _logger.Info("开始卸载模块...");
            
            // 逆序停止和释放模块
            for (int i = _loadedModules.Count - 1; i >= 0; i--)
            {
                try
                {
                    var module = _loadedModules[i];
                    module.Stop();
                    
                    if (module is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                    
                    _logger.Info($"已卸载模块: {module.Name}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"卸载模块 {i} 时出错: {ex.Message}", ex);
                }
            }
            
            _loadedModules.Clear();
        }
    }
}
```

## 4. 主程序(Shell)实现

### 4.1 程序入口

**文件**: `Shell/Program.cs`

```csharp
using System;
using System.Windows.Forms;
using Shell.Bootstrapper;

namespace Shell
{
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // 创建并运行应用
                var bootstrapper = new Bootstrapper();
                bootstrapper.Run();
            }
            catch (Exception ex)
            {
                // 全局异常处理
                MessageBox.Show($"应用程序启动失败: {ex.Message}\n详情请查看日志", 
                               "致命错误", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
                Application.Exit();
            }
        }
    }
}
```

### 4.2 Bootstrapper 初始化

**文件**: `Shell/Bootstrapper/Bootstrapper.cs`

```csharp
using System;
using System.IO;
using System.Windows.Forms;
using DryIoc;
using Contracts;
using Shell.Views;

namespace Shell.Bootstrapper
{
    public class Bootstrapper
    {
        private IContainer _container;
        private ModuleLoader _moduleLoader;
        private MainForm _mainForm;

        public void Run()
        {
            try
            {
                // 1. 初始化 IoC 容器
                _container = ContainerBootstrapper.CreateContainer();
                
                // 2. 获取日志服务
                var logger = _container.Resolve<ILogger>();
                logger.Info("应用程序启动");
                
                // 3. 创建模块加载器
                _moduleLoader = new ModuleLoader(_container, logger);
                
                // 4. 创建主窗体
                _mainForm = _container.Resolve<MainForm>();
                
                // 5. 加载模块
                var modulesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Modules");
                var modules = _moduleLoader.LoadModules(modulesPath);
                
                // 6. 将模块UI添加到主窗体
                foreach (var module in modules)
                {
                    _mainForm.AddModuleTab(module.Name, module.GetView());
                }
                
                // 7. 发布系统启动事件
                var eventAggregator = _container.Resolve<IEventAggregator>();
                eventAggregator.GetEvent<SystemStartupEvent>().Publish();
                
                // 8. 运行应用程序
                Application.Run(_mainForm);
            }
            catch (Exception ex)
            {
                // 全局异常处理
                var logger = _container?.Resolve<ILogger>();
                logger?.Error("Bootstrapper 运行失败", ex);
                
                MessageBox.Show($"应用程序初始化失败: {ex.Message}\n详情请查看日志", 
                               "初始化错误", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
                throw;
            }
            finally
            {
                // 应用程序退出时清理
                _moduleLoader?.UnloadModules();
                _container?.Dispose();
            }
        }
    }
}
```

### 4.3 主窗体实现

**文件**: `Shell/Views/MainForm.cs`

```csharp
using System;
using System.Windows.Forms;
using Contracts;

namespace Shell.Views
{
    public partial class MainForm : Form, IView
    {
        private readonly IPresenter _presenter;
        private int _tabCount = 0;

        public IPresenter Presenter
        {
            get => _presenter;
            set { /* Presenter 通常在构造函数中设置 */ }
        }

        public MainForm()
        {
            InitializeComponent();
            SetupUI();
        }

        private void SetupUI()
        {
            // 设置主窗体属性
            Text = "工业上位机系统 - 模块化框架";
            WindowState = FormWindowState.Maximized;
            IsMdiContainer = true;
            
            // 创建TabControl用于承载模块
            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Name = "mainTabControl"
            };
            
            Controls.Add(tabControl);
        }

        public void AddModuleTab(string title, UserControl view)
        {
            var tabPage = new TabPage(title)
            {
                Name = $"tab{++_tabCount}",
                AutoScroll = true
            };
            
            // 设置视图大小并添加到TabPage
            view.Dock = DockStyle.Fill;
            tabPage.Controls.Add(view);
            
            // 添加到TabControl
            mainTabControl.TabPages.Add(tabPage);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 发布系统关闭事件
            var eventAggregator = ((IContainer)_presenter).Resolve<IEventAggregator>();
            eventAggregator.GetEvent<SystemShutdownEvent>().Publish();
            
            base.OnFormClosing(e);
        }

        public void ShowError(string message)
        {
            MessageBox.Show(this, message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public void ShowInfo(string message)
        {
            MessageBox.Show(this, message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public void RefreshUI()
        {
            // 刷新所有控件
            foreach (Control control in Controls)
            {
                control.Refresh();
            }
        }
    }
}
```

## 5. 模块开发规范与示例

### 5.1 模块接口定义

**文件**: `Contracts/IModule.cs`

```csharp
using System.Windows.Forms;
using Prism.Events;

namespace Contracts
{
    /// <summary>
    /// 模块接口，所有功能模块必须实现此接口
    /// </summary>
    public interface IModule
    {
        /// <summary>
        /// 模块名称（用于UI显示）
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 模块描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 模块初始化（订阅事件等）
        /// </summary>
        void Initialize();
        
        /// <summary>
        /// 获取模块的UI视图
        /// </summary>
        UserControl GetView();
        
        /// <summary>
        /// 模块启动（系统就绪后调用）
        /// </summary>
        void Start();
        
        /// <summary>
        /// 模块停止
        /// </summary>
        void Stop();
    }
}
```

### 5.2 设备监控模块示例

#### 5.2.1 模块定义

**文件**: `Modules.Sources/DeviceMonitoring/DeviceModule.cs`

```csharp
using System.ComponentModel.Composition;
using Contracts;
using Contracts.Events;
using Prism.Events;
using DryIoc;

namespace DeviceMonitoring
{
    [Export(typeof(IModule))]
    public class DeviceModule : IModule, IDisposable
    {
        private readonly IEventAggregator _eventAggregator;
        private readonly DevicePresenter _presenter;
        private bool _isDisposed;

        [ImportingConstructor]
        public DeviceModule(IEventAggregator eventAggregator, DevicePresenter presenter)
        {
            _eventAggregator = eventAggregator;
            _presenter = presenter;
        }

        public string Name => "设备监控";
        
        public string Description => "实时监控设备连接状态和运行参数";

        public void Initialize()
        {
            // 订阅系统启动事件
            _eventAggregator.GetEvent<SystemStartupEvent>()
                .Subscribe(OnSystemStartup, ThreadOption.UIThread);
                
            // 订阅设备连接事件
            _eventAggregator.GetEvent<DeviceConnectionEvent>()
                .Subscribe(OnDeviceConnectionChanged, ThreadOption.UIThread);
        }

        private void OnSystemStartup()
        {
            _presenter.LoadDeviceList();
            _presenter.StartMonitoring();
        }

        private void OnDeviceConnectionChanged(DeviceConnectionStatus status)
        {
            _presenter.UpdateDeviceStatus(status);
        }

        public UserControl GetView() => _presenter.View;

        public void Start()
        {
            _presenter.StartMonitoring();
        }

        public void Stop()
        {
            _presenter.StopMonitoring();
        }

        public void Dispose()
        {
            if (_isDisposed) return;
            
            // 取消事件订阅
            _eventAggregator.GetEvent<SystemStartupEvent>()
                .Unsubscribe(OnSystemStartup);
                
            _eventAggregator.GetEvent<DeviceConnectionEvent>()
                .Unsubscribe(OnDeviceConnectionChanged);
                
            // 释放资源
            (_presenter.View as IDisposable)?.Dispose();
            
            _isDisposed = true;
        }
    }
}
```

#### 5.2.2 MVP 实现

**文件**: `Modules.Sources/DeviceMonitoring/DeviceView.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Contracts;

namespace DeviceMonitoring
{
    public partial class DeviceView : UserControl, IView
    {
        private DevicePresenter _presenter;

        public DeviceView(DevicePresenter presenter)
        {
            InitializeComponent();
            _presenter = presenter;
            _presenter.View = this;
            
            SetupUI();
            BindEvents();
        }

        private void SetupUI()
        {
            // 设置DataGridView
            deviceDataGridView.AutoGenerateColumns = false;
            deviceDataGridView.Dock = DockStyle.Fill;
            
            // 添加列
            var columns = new[]
            {
                new DataGridViewTextBoxColumn { Name = "IdColumn", HeaderText = "设备ID", DataPropertyName = "DeviceId", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "NameColumn", HeaderText = "设备名称", DataPropertyName = "Name", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "StatusColumn", HeaderText = "状态", DataPropertyName = "Status", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "ValueColumn", HeaderText = "当前值", DataPropertyName = "CurrentValue", Width = 100 }
            };
            
            foreach (var column in columns)
            {
                deviceDataGridView.Columns.Add(column);
            }
        }

        private void BindEvents()
        {
            refreshButton.Click += (s, e) => _presenter.LoadDeviceList();
            connectAllButton.Click += (s, e) => _presenter.ConnectAllDevices();
            disconnectAllButton.Click += (s, e) => _presenter.DisconnectAllDevices();
        }

        public void ShowDevices(List<DeviceViewModel> devices)
        {
            deviceDataGridView.DataSource = null;
            deviceDataGridView.DataSource = devices;
        }

        public void UpdateDeviceStatus(string deviceId, string status)
        {
            foreach (DataGridViewRow row in deviceDataGridView.Rows)
            {
                if (row.DataBoundItem is DeviceViewModel device && device.DeviceId == deviceId)
                {
                    device.Status = status;
                    row.Cells["StatusColumn"].Value = status;
                    break;
                }
            }
        }

        public IPresenter Presenter { get; set; }
        
        public void ShowError(string message)
        {
            MessageBox.Show(this, message, "设备监控错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public void ShowInfo(string message)
        {
            MessageBox.Show(this, message, "设备监控", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public void RefreshUI()
        {
            deviceDataGridView.Refresh();
        }
    }
}
```

**文件**: `Modules.Sources/DeviceMonitoring/DevicePresenter.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Prism.Events;

namespace DeviceMonitoring
{
    public class DevicePresenter : IPresenter
    {
        private readonly IDeviceService _deviceService;
        private readonly IEventAggregator _eventAggregator;
        private Timer _monitoringTimer;
        
        public IView View { get; set; }
        public bool IsMonitoring { get; private set; }

        public DevicePresenter(IDeviceService deviceService, IEventAggregator eventAggregator)
        {
            _deviceService = deviceService;
            _eventAggregator = eventAggregator;
        }

        public void LoadDeviceList()
        {
            try
            {
                var devices = _deviceService.GetDevices()
                    .Select(d => new DeviceViewModel
                    {
                        DeviceId = d.Id,
                        Name = d.Name,
                        Status = d.IsConnected ? "已连接" : "未连接",
                        CurrentValue = d.CurrentValue.ToString()
                    })
                    .ToList();
                    
                ((DeviceView)View).ShowDevices(devices);
            }
            catch (Exception ex)
            {
                View.ShowError($"加载设备列表失败: {ex.Message}");
            }
        }

        public void StartMonitoring()
        {
            if (IsMonitoring) return;
            
            _monitoringTimer = new Timer { Interval = 1000 };
            _monitoringTimer.Tick += (s, e) => UpdateDeviceValues();
            _monitoringTimer.Start();
            
            IsMonitoring = true;
        }

        public void StopMonitoring()
        {
            if (!IsMonitoring) return;
            
            _monitoringTimer?.Stop();
            _monitoringTimer?.Dispose();
            _monitoringTimer = null;
            
            IsMonitoring = false;
        }

        private void UpdateDeviceValues()
        {
            try
            {
                var devices = _deviceService.GetDevices();
                foreach (var device in devices)
                {
                    // 发布实时数据事件
                    _eventAggregator.GetEvent<RealTimeDataEvent>().Publish(
                        new Dictionary<string, object> 
                        { 
                            { device.Id, device.CurrentValue } 
                        });
                    
                    // 更新UI
                    ((DeviceView)View).UpdateDeviceStatus(
                        device.Id, 
                        device.IsConnected ? "已连接" : "未连接");
                }
            }
            catch (Exception ex)
            {
                // 避免单个设备问题影响整体
                View.ShowError($"更新设备值时出错: {ex.Message}");
            }
        }

        public void ConnectAllDevices()
        {
            try
            {
                _deviceService.ConnectAll();
                LoadDeviceList();
                
                // 通知其他模块
                _eventAggregator.GetEvent<DeviceConnectionEvent>().Publish(
                    new DeviceConnectionStatus 
                    { 
                        DeviceId = "ALL", 
                        IsConnected = true, 
                        Timestamp = DateTime.Now 
                    });
            }
            catch (Exception ex)
            {
                View.ShowError($"连接所有设备失败: {ex.Message}");
            }
        }

        public void DisconnectAllDevices()
        {
            try
            {
                _deviceService.DisconnectAll();
                LoadDeviceList();
                
                // 通知其他模块
                _eventAggregator.GetEvent<DeviceConnectionEvent>().Publish(
                    new DeviceConnectionStatus 
                    { 
                        DeviceId = "ALL", 
                        IsConnected = false, 
                        Timestamp = DateTime.Now 
                    });
            }
            catch (Exception ex)
            {
                View.ShowError($"断开所有设备失败: {ex.Message}");
            }
        }

        public void UpdateDeviceStatus(DeviceConnectionStatus status)
        {
            if (status.DeviceId == "ALL")
            {
                LoadDeviceList();
                return;
            }
            
            ((DeviceView)View).UpdateDeviceStatus(
                status.DeviceId, 
                status.IsConnected ? "已连接" : "未连接");
        }
    }
    
    public class DeviceViewModel
    {
        public string DeviceId { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public string CurrentValue { get; set; }
    }
}
```

## 6. 模块通信机制详解

### 6.1 事件发布/订阅模式

#### 发布事件
```csharp
// 在任意模块中发布事件
var eventAggregator = _container.Resolve<IEventAggregator>();
eventAggregator.GetEvent<AlarmEvent>().Publish(new AlarmInfo
{
    AlarmId = "ALM001",
    Message = "电机过载",
    Level = AlarmLevel.Critical,
    TriggerTime = DateTime.Now,
    Source = "MotorControl"
});
```

#### 订阅事件
```csharp
// 在需要接收事件的模块中
_eventAggregator.GetEvent<AlarmEvent>()
    .Subscribe(OnAlarmReceived, ThreadOption.UIThread, keepSubscriberReferenceAlive: false);

private void OnAlarmReceived(AlarmInfo alarm)
{
    // 在UI线程上处理报警
    ShowAlarm(alarm);
}
```

### 6.2 通信最佳实践

1. **线程安全**
   - 使用 `ThreadOption.UIThread` 确保UI更新在主线程执行
   - 避免在事件处理中执行耗时操作

2. **内存管理**
   - 设置 `keepSubscriberReferenceAlive: false` 防止内存泄漏
   - 在模块释放时取消订阅

3. **错误处理**
   - 事件处理中捕获所有异常，防止一个模块崩溃影响整个系统
   - 记录详细错误日志

4. **事件粒度**
   - 事件定义应足够具体，避免一个事件承担过多职责
   - 例如：区分 `DeviceConnectedEvent` 和 `DeviceDisconnectedEvent`

## 7. 部署与维护指南

### 7.1 部署结构

```
IndustrialHMI/
├── Shell.exe
├── Shell.exe.config
├── Contracts.dll
├── Services.dll
├── DryIoc.dll
├── Prism.Core.dll
├── System.ComponentModel.Composition.dll
├── Modules/                # 模块目录
│   ├── DeviceMonitoring.dll
│   ├── AlarmManagement.dll
│   └── ...
├── Config/                 # 配置文件
│   ├── appsettings.json
│   └── modules.config      # 模块启用配置
└── Logs/                   # 日志目录
```

### 7.2 模块管理

#### 7.2.1 模块启用/禁用
通过 `Config/modules.config` 控制模块加载：

```json
{
  "enabledModules": [
    "DeviceMonitoring",
    "AlarmManagement",
    "RecipeManagement"
  ],
  "disabledModules": [
    "ReportGeneration"
  ]
}
```

#### 7.2.2 模块热更新流程
1. 停止相关模块（通过UI或API）
2. 备份旧模块DLL
3. 替换新模块DLL
4. 重新加载模块（无需重启主程序）

### 7.3 异常处理与日志

#### 7.3.1 全局异常处理
```csharp
// 在Program.cs中
Application.ThreadException += (s, e) => 
{
    var logger = container.Resolve<ILogger>();
    logger.Error("UI线程异常", e.Exception);
    MessageBox.Show($"发生未处理异常: {e.Exception.Message}");
};

AppDomain.CurrentDomain.UnhandledException += (s, e) => 
{
    var logger = container.Resolve<ILogger>();
    logger.Error("未处理异常", (Exception)e.ExceptionObject);
};
```

#### 7.3.2 模块级异常隔离
```csharp
// 在模块加载器中
try
{
    module.Initialize();
}
catch (Exception ex)
{
    _logger.Error($"模块 {module.Name} 初始化失败", ex);
    // 标记模块为不可用，但继续加载其他模块
}
```

## 8. 性能优化与工业级考量

### 8.1 内存管理
1. **弱引用事件订阅**：确保 `keepSubscriberReferenceAlive: false`
2. **资源及时释放**：模块实现 `IDisposable`，在 `Stop()` 和 `Dispose()` 中释放资源
3. **避免闭包内存泄漏**：谨慎使用 lambda 表达式

### 8.2 工业环境适应性
1. **通信稳定性**
   - 实现PLC通信重连机制
   - 添加通信超时和错误处理
   - 支持多种工业协议（Modbus, OPC UA等）

2. **7×24小时运行**
   - 定期内存清理
   - 避免使用可能导致内存泄漏的第三方组件
   - 实现看门狗机制

3. **UI响应性**
   - 长时间操作使用后台线程
   - 避免UI线程阻塞
   - 优化数据绑定性能

### 8.3 安全性考虑
1. **模块验证**
   - DLL签名验证
   - 模块来源检查

2. **权限控制**
   - 基于角色的模块访问控制
   - 操作权限验证

3. **数据安全**
   - 敏感数据加密存储
   - 通信数据加密

## 9. 常见问题与解决方案

### 9.1 模块加载失败
**现象**：模块未出现在主界面中  
**排查步骤**：
1. 检查模块是否放在正确的 `Modules` 目录
2. 查看日志中是否有模块加载错误
3. 确认模块是否实现了 `IModule` 接口
4. 检查模块是否标记 `[Export(typeof(IModule))]` 特性
5. 验证模块依赖是否满足

### 9.2 事件通信不工作
**现象**：模块间事件无法传递  
**排查步骤**：
1. 检查事件订阅是否在模块初始化时完成
2. 确认事件发布和订阅使用相同的事件类型
3. 验证 `ThreadOption` 设置是否正确
4. 检查是否在适当时候取消订阅
5. 使用日志跟踪事件发布和订阅

### 9.3 内存泄漏
**现象**：长时间运行后内存持续增长  
**解决方案**：
1. 确保所有事件订阅设置 `keepSubscriberReferenceAlive: false`
2. 模块卸载时明确取消所有订阅
3. 实现模块的 `Dispose` 方法
4. 定期进行内存分析（使用dotMemory等工具）

## 10. 附录

### 10.1 模块开发检查清单
- [ ] 实现 `IModule` 接口
- [ ] 标记 `[Export(typeof(IModule))]` 特性
- [ ] 通过构造函数注入依赖
- [ ] 实现 MVP 模式
- [ ] 订阅/发布事件时考虑线程安全
- [ ] 实现资源释放逻辑
- [ ] 添加适当的错误处理
- [ ] 编写模块描述文档

### 10.2 项目构建脚本示例

**文件**: `build/build.ps1`

```powershell
# 构建脚本
$ErrorActionPreference = "Stop"

# 配置
$SolutionFile = "..\IndustrialHMI.sln"
$OutputDir = "..\Output"
$ModulesDir = "..\Output\Modules"

# 清理
Write-Host "清理输出目录..."
Remove-Item $OutputDir -Recurse -Force -ErrorAction Ignore
New-Item -ItemType Directory -Path $OutputDir | Out-Null
New-Item -ItemType Directory -Path $ModulesDir | Out-Null

# 构建主程序
Write-Host "构建主程序..."
dotnet build $SolutionFile -c Release /p:OutputPath=$OutputDir

# 构建所有模块
Write-Host "构建功能模块..."
Get-ChildItem -Path "..\Modules.Sources" -Directory | ForEach-Object {
    $modulePath = $_.FullName
    $moduleName = $_.Name
    
    Write-Host "构建模块: $moduleName"
    dotnet build "$modulePath\$moduleName.csproj" -c Release /p:OutputPath=$ModulesDir
    
    # 复制PDB文件用于调试
    Copy-Item "$modulePath\bin\Release\*.pdb" $ModulesDir -ErrorAction Ignore
}

# 复制配置文件
Write-Host "复制配置文件..."
Copy-Item "..\Shell\appsettings.json" $OutputDir
New-Item -ItemType Directory -Path "$OutputDir\Config" | Out-Null
Copy-Item "..\Config\*" "$OutputDir\Config\" -Recurse

# 创建部署包
$timestamp = Get-Date -Format "yyyyMMddHHmm"
Compress-Archive -Path $OutputDir -DestinationPath "..\IndustrialHMI_Deploy_$timestamp.zip"

Write-Host "构建完成! 部署包已创建: ..\IndustrialHMI_Deploy_$timestamp.zip" -ForegroundColor Green
```

### 10.3 模块配置示例

**文件**: `Config/modules.config`

```json
{
  "moduleSettings": {
    "DeviceMonitoring": {
      "refreshInterval": 1000,
      "autoConnectOnStartup": true,
      "devices": [
        { "id": "PLC001", "name": "主PLC", "address": "************", "port": 502 },
        { "id": "SCADA001", "name": "SCADA系统", "address": "************", "port": 502 }
      ]
    },
    "AlarmManagement": {
      "maxHistory": 1000,
      "autoAcknowledge": false,
      "soundAlert": true
    }
  }
}
```

---

**文档版本**: 1.0  
**最后更新**: 2023年10月15日  
**适用框架版本**: WinForms MVP Modular Framework 2.0  
**作者**: 工业自动化架构团队
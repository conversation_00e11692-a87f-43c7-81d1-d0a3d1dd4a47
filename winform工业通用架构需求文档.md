# WinForms 工业上位机模块化框架需求文档

## 1. 项目概述

### 1.1 项目背景
本项目旨在开发一个工业级 WinForms 上位机软件框架，用于快速构建工业设备 HMI 系统。框架需支持"搭积木式"开发模式，使开发人员能够快速、安全地搭建工业设备软件，同时保证系统的高解耦性、鲁棒性和易扩展性。

### 1.2 项目目标
- 实现模块化架构，支持功能模块热插拔
- 采用 MVP 模式分离 UI 逻辑与业务逻辑
- 模块间通过事件总线通信，实现完全解耦
- 支持工业现场常见功能（设备监控、报警管理、配方管理等）
- 确保框架稳定可靠，满足工业环境7×24小时运行要求

### 1.3 核心价值
- **快速开发**：新增功能只需添加新模块，无需修改主程序
- **高度解耦**：模块间无直接引用，通过接口和事件通信
- **安全可靠**：模块异常隔离，不影响主程序和其他模块
- **易于维护**：每个模块独立开发、测试和部署
- **工业适用**：兼容工业现场常用通信协议和硬件设备

## 2. 架构设计

### 2.1 整体架构
采用"主程序(Shell) + 模块化插件"的架构模式，基于 MVP 设计模式，结合 DryIoc、EventAggregator 和 MEF 实现。

```
┌─────────────────────────────────────────────┐
│               主程序（Shell）               │
│  - 初始化 DryIoc 容器                       │
│  - 初始化 EventAggregator                   │
│  - 调用 MEF 扫描插件目录                    │
│  - 注册模块到 DryIoc 容器                   │
│  - 加载 UI 模块（UserControl）              │
└─────────────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────┐
│             模块（ModuleX.dll）             │
│  - [Export(typeof(IModule))]                │
│  - 实现 IModule 接口                        │
│  - 通过构造函数注入依赖                     │
│  - 通过事件总线进行模块间通信               │
└─────────────────────────────────────────────┘
```

### 2.2 核心组件
| 组件 | 职责 | 说明 |
|------|------|------|
| **Shell** | 主程序入口 | 负责初始化容器、加载模块、管理主界面 |
| **DryIoc** | 依赖注入容器 | 管理对象生命周期，实现依赖注入 |
| **EventAggregator** | 事件总线 | 实现模块间松耦合通信 |
| **MEF** | 模块加载器 | 动态扫描和加载模块 DLL |
| **MVP** | 架构模式 | 分离 UI 逻辑与业务逻辑 |

## 3. 技术栈

### 3.1 核心技术
| 技术 | 版本 | 用途 |
|------|------|------|
| .NET Framework | 4.8+ | 基础运行环境 |
| WinForms | - | UI 框架 |
| DryIoc | 5.0+ | 依赖注入容器 |
| MEF | .NET 内置 | 模块化扩展框架 |
| Prism.EventAggregator | 8.1+ | 事件聚合器 |

### 3.2 辅助技术
| 技术 | 用途 |
|------|------|
| Serilog | 统一日志记录 |
| NLog | 备选日志方案 |
| Newtonsoft.Json | 配置文件处理 |
| Modbus.NET | PLC 通信支持 |
| LiveCharts | 实时数据可视化 |

## 4. 模块划分

### 4.1 核心模块
| 模块 | 描述 | 依赖 |
|------|------|------|
| **Shell.Core** | 主程序核心 | DryIoc, MEF, EventAggregator |
| **Shell.Contracts** | 共享接口定义 | - |
| **Shell.Services** | 基础服务实现 | Shell.Contracts |

### 4.2 功能模块（示例）
| 模块 | 描述 | 通信事件 |
|------|------|----------|
| **DeviceMonitoring** | 设备监控 | DeviceStatusEvent, ConnectionEvent |
| **AlarmManagement** | 报警管理 | AlarmEvent, AcknowledgeEvent |
| **RecipeManagement** | 配方管理 | RecipeLoadEvent, RecipeSaveEvent |
| **TrendAnalysis** | 趋势分析 | DataPointEvent, TrendRequestEvent |
| **UserManagement** | 用户管理 | LoginEvent, PermissionEvent |
| **ReportGeneration** | 报表生成 | ReportRequestEvent, ReportGeneratedEvent |

## 5. 详细功能需求

### 5.1 主程序(Shell)功能
1. **容器初始化**
   - 初始化 DryIoc 容器，注册核心服务
   - 配置单例、瞬态等生命周期管理
   - 注册事件聚合器为单例

2. **模块加载**
   - 扫描 `Modules` 目录下的所有 DLL
   - 通过 MEF 发现实现 `IModule` 接口的类型
   - 使用 DryIoc 解析模块依赖并创建实例
   - 捕获并记录模块加载过程中的异常

3. **UI 管理**
   - 提供 Tab 页或区域管理模块 UI
   - 支持模块 UI 的动态添加、移除
   - 处理模块 UI 的生命周期

4. **系统服务**
   - 提供全局异常处理机制
   - 实现系统级配置管理
   - 提供统一日志记录接口

### 5.2 模块功能规范
1. **模块接口要求**
   - 必须实现 `IModule` 接口
   - 必须标记 `[Export(typeof(IModule))]` 特性
   - 必须通过构造函数注入所需依赖

2. **MVP 实现规范**
   - 每个模块必须有对应的 View、Presenter
   - View 必须继承 UserControl 并实现 IView 接口
   - Presenter 必须通过接口与 View 交互
   - 不得直接引用其他模块的实现类

3. **通信规范**
   - 模块间通信必须通过 EventAggregator
   - 必须定义明确的事件类型
   - 事件处理必须考虑线程安全
   - 必须在适当时候取消订阅

### 5.3 事件通信规范
1. **事件定义**
   - 所有事件必须继承自 `PubSubEvent<T>` 或 `PubSubEvent`
   - 事件类型必须放在 Contracts 程序集中
   - 事件命名规范：`[业务]Event`

2. **事件发布**
   - 发布事件时必须考虑异常处理
   - 不得在事件处理中执行耗时操作
   - 发布事件前应检查是否已取消订阅

3. **事件订阅**
   - 订阅必须在模块初始化时完成
   - 必须在模块释放时取消订阅
   - 订阅方法必须考虑 UI 线程调度

## 6. 开发环境要求

### 6.1 硬件要求
- 开发机：Intel i5 或更高处理器，16GB RAM，500GB SSD
- 测试环境：工业级 PC 或与目标设备匹配的硬件

### 6.2 软件环境
- 操作系统：Windows 10/11 (64位)
- 开发工具：VSCode 1.80+ (配合 C# Dev Kit 扩展)
- .NET SDK：.NET Framework 4.8 Developer Pack
- 必要扩展：
  - C# for Visual Studio Code (powered by OmniSharp)
  - C# Dev Kit
  - NuGet Package Manager
  - .NET Interactive Notebooks

### 6.3 项目结构
```
IndustrialHMI/
├── .vscode/                    # VSCode 配置
├── Shell/                      # 主程序
│   ├── Properties/
│   ├── Views/
│   │   └── MainForm.cs         # 主界面
│   ├── Presenters/
│   │   └── ShellPresenter.cs
│   ├── Bootstrapper/
│   │   └── Bootstrapper.cs     # 初始化逻辑
│   ├── Program.cs
│   └── IndustrialHMI.csproj
├── Contracts/                  # 共享接口
│   ├── IModule.cs
│   ├── IView.cs
│   ├── Events/                 # 事件定义
│   └── Contracts.csproj
├── Services/                   # 核心服务
│   ├── Logging/
│   ├── Communication/
│   └── Services.csproj
├── Modules/                    # 模块目录（运行时加载）
│   └── .gitkeep                # 保留空目录
├── Modules.Sources/            # 模块源代码（开发用）
│   ├── DeviceMonitoring/       # 设备监控模块
│   ├── AlarmManagement/        # 报警管理模块
│   └── ...                     # 其他模块
├── build/                      # 构建脚本
│   └── build.ps1               # PowerShell 构建脚本
├── tests/                      # 测试代码
├── IndustrialHMI.sln           # 解决方案文件
└── README.md
```

## 7. 实现步骤

### 7.1 框架搭建
1. 创建解决方案和核心项目
   - 创建 IndustrialHMI.sln
   - 添加 Shell、Contracts、Services 项目
   - 配置项目引用关系

2. 配置依赖注入
   - 安装 DryIoc NuGet 包
   - 实现容器初始化逻辑
   - 注册核心服务

3. 实现事件总线
   - 安装 Prism.Core NuGet 包
   - 创建 EventAggregator 实例
   - 验证事件发布订阅机制

4. 实现模块加载
   - 配置 MEF 目录扫描
   - 实现模块注册和初始化
   - 验证模块热加载功能

### 7.2 示例模块开发
1. 创建设备监控模块
   - 定义设备状态事件
   - 实现 MVP 三件套
   - 添加 MEF 导出特性
   - 实现事件订阅/发布

2. 创建报警管理模块
   - 订阅设备状态事件
   - 实现报警逻辑
   - 发布报警确认事件

3. 验证模块间通信
   - 测试事件发布订阅
   - 验证异常隔离机制
   - 测试模块热插拔

### 7.3 工业特性增强
1. 添加 PLC 通信支持
   - 集成 Modbus.NET
   - 实现通信服务抽象
   - 添加连接状态管理

2. 实现数据持久化
   - 配置本地数据库
   - 实现报警历史存储
   - 添加配方管理功能

3. 增强 UI 体验
   - 添加实时趋势图
   - 实现多语言支持
   - 添加用户权限控制

## 8. 测试要求

### 8.1 单元测试
1. 模块加载测试
   - 验证正常模块加载
   - 验证异常模块处理
   - 测试模块依赖注入

2. 事件通信测试
   - 验证事件发布订阅
   - 测试跨线程事件处理
   - 验证订阅取消机制

3. MVP 逻辑测试
   - 验证 Presenter 业务逻辑
   - 测试 View 更新机制
   - 验证异常处理流程

### 8.2 集成测试
1. 模块组合测试
   - 测试多模块协同工作
   - 验证模块加载顺序影响
   - 测试资源竞争情况

2. 异常场景测试
   - 模拟模块异常崩溃
   - 测试长时间运行稳定性
   - 验证内存泄漏情况

3. 工业环境模拟
   - 模拟 PLC 通信中断
   - 测试高频率数据更新
   - 验证系统资源占用

### 8.3 验收标准
1. 模块热插拔
   - 新增模块无需重新编译主程序
   - 删除模块不影响系统运行
   - 模块异常不影响其他功能

2. 解耦性验证
   - 模块间无直接引用
   - 通信仅通过事件总线
   - 接口定义清晰明确

3. 工业适用性
   - 支持 7×24 小时稳定运行
   - 内存占用稳定无泄漏
   - UI 响应及时无卡顿

## 9. 交付物

1. **源代码**
   - 完整的框架源代码
   - 2-3 个示例功能模块
   - 自动化构建脚本

2. **文档**
   - 框架使用手册
   - 模块开发指南
   - API 参考文档
   - 部署配置说明

3. **示例项目**
   - 简单工业设备模拟器
   - 完整的功能演示项目
   - 常见问题解决方案

## 10. 附录

### 10.1 关键接口定义

**IModule.cs**
```csharp
public interface IModule
{
    /// <summary>
    /// 模块名称（用于UI显示）
    /// </summary>
    string Name { get; }
    
    /// <summary>
    /// 模块描述
    /// </summary>
    string Description { get; }
    
    /// <summary>
    /// 模块初始化（订阅事件等）
    /// </summary>
    void Initialize();
    
    /// <summary>
    /// 获取模块的UI视图
    /// </summary>
    UserControl GetView();
    
    /// <summary>
    /// 模块启动（系统就绪后调用）
    /// </summary>
    void Start();
    
    /// <summary>
    /// 模块停止
    /// </summary>
    void Stop();
    
    /// <summary>
    /// 释放资源
    /// </summary>
    void Dispose();
}
```

**IView.cs**
```csharp
public interface IView
{
    /// <summary>
    /// 获取或设置关联的Presenter
    /// </summary>
    IPresenter Presenter { get; set; }
    
    /// <summary>
    /// 显示错误消息
    /// </summary>
    void ShowError(string message);
    
    /// <summary>
    /// 显示信息消息
    /// </summary>
    void ShowInfo(string message);
    
    /// <summary>
    /// 刷新UI
    /// </summary>
    void RefreshUI();
}
```

### 10.2 事件定义示例

```csharp
// 设备连接状态事件
public class DeviceConnectionEvent : PubSubEvent<DeviceConnectionStatus> { }

public class DeviceConnectionStatus
{
    public string DeviceId { get; set; }
    public bool IsConnected { get; set; }
    public DateTime Timestamp { get; set; }
}

// 报警事件
public class AlarmEvent : PubSubEvent<AlarmInfo> { }

public class AlarmInfo
{
    public string AlarmId { get; set; }
    public string Message { get; set; }
    public AlarmLevel Level { get; set; }
    public DateTime TriggerTime { get; set; }
    public bool IsAcknowledged { get; set; }
}

public enum AlarmLevel
{
    Info,
    Warning,
    Error,
    Critical
}
```

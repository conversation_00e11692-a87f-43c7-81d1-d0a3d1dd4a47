# WinForms 模块化架构开发约束

## 技术栈
.NET Framework 4.8 + WinForms + DryIoc + 自实现EventAggregator

## 模块开发模板
```csharp
public class SampleModule : IModule
{
    // 依赖注入属性
    public IEventAggregator EventAggregator { get; set; }
    public IServiceType ServiceName { get; set; }

    public string Name => "模块名称";
    public string Description => "模块描述";

    public void Initialize()
    {
        // 1. 验证依赖注入
        if (EventAggregator == null) throw new InvalidOperationException("EventAggregator 未注入");

        // 2. 订阅事件
        EventAggregator.GetEvent<SomeEvent>()
            .Subscribe(OnSomeEvent, ThreadOption.UIThread, keepSubscriberReferenceAlive: false);
    }

    public void Dispose()
    {
        EventAggregator?.GetEvent<SomeEvent>().Unsubscribe(OnSomeEvent);
    }
}
```

## 事件通信
```csharp
// 发布事件
EventAggregator.GetEvent<SomeEvent>().Publish(eventData);

// 订阅事件 - 必须设置 keepSubscriberReferenceAlive: false
EventAggregator.GetEvent<SomeEvent>()
    .Subscribe(OnSomeEvent, ThreadOption.UIThread, keepSubscriberReferenceAlive: false);
```

## UI线程安全
```csharp
private void SafeUpdateUI(Action action)
{
    if (InvokeRequired) BeginInvoke(action);
    else action();
}
```

## 必须遵守
1. 事件订阅设置 `keepSubscriberReferenceAlive: false`
2. 模块实现Dispose并取消事件订阅
3. UI更新在UI线程执行
4. 异常处理不影响其他模块
5. 依赖注入验证在Initialize中

## 禁止事项
1. 模块间直接引用
2. 构造函数中复杂逻辑
3. 事件处理中抛出未捕获异常
4. 长时间阻塞UI线程
5. 忘记取消事件订阅

## 常见问题
- **模块未加载**: 检查IModule接口实现和无参构造函数
- **依赖注入失败**: 确保服务已注册，属性public且有setter
- **事件未接收**: 确认事件类型匹配，在Initialize中订阅
- **内存泄漏**: 检查keepSubscriberReferenceAlive设置和Dispose实现

## 6. 常见问题和解决方案

### 6.1 模块加载问题

#### 问题：模块未被加载
**症状**：模块DLL存在但未出现在主界面
**排查步骤**：
1. 检查模块是否实现了IModule接口
2. 检查模块是否有无参构造函数
3. 查看日志中的模块加载错误信息
4. 验证模块DLL的依赖是否完整

**解决方案**：
```csharp
// 确保模块正确实现接口
public class MyModule : IModule
{
    // 必须有无参构造函数
    public MyModule() { }

    // 实现所有接口方法
    public string Name => "我的模块";
    public string Description => "模块描述";
    // ... 其他方法
}
```

#### 问题：依赖注入失败
**症状**：模块属性为null，抛出NullReferenceException
**解决方案**：
```csharp
public void Initialize()
{
    // 在使用前验证依赖注入
    if (EventAggregator == null)
        throw new InvalidOperationException("EventAggregator未注入，请检查DryIoc容器配置");

    if (DeviceService == null)
        throw new InvalidOperationException("DeviceService未注入，请检查服务注册");
}
```

### 6.2 事件通信问题

#### 问题：事件未被接收
**排查步骤**：
1. 检查事件订阅是否在Initialize()方法中
2. 验证事件类型是否匹配
3. 确认ThreadOption设置正确
4. 检查是否过早取消订阅

**解决方案**：
```csharp
// 正确的事件订阅方式
public void Initialize()
{
    EventAggregator.GetEvent<MyEvent>()
        .Subscribe(OnMyEvent, ThreadOption.UIThread, keepSubscriberReferenceAlive: false);
}

private void OnMyEvent(MyEventData data)
{
    // 添加日志确认事件被接收
    Logger?.Debug($"接收到事件: {data}");

    // 处理事件
    ProcessEvent(data);
}
```

#### 问题：内存泄漏
**症状**：长时间运行后内存持续增长
**解决方案**：
```csharp
// 确保正确取消订阅
public void Dispose()
{
    try
    {
        // 取消所有事件订阅
        EventAggregator?.GetEvent<MyEvent>().Unsubscribe(OnMyEvent);

        // 清空事件处理器引用
        _eventHandlers?.Clear();
    }
    catch (Exception ex)
    {
        Logger?.Error($"取消订阅失败: {ex.Message}", ex);
    }
}
```

### 6.3 UI更新问题

#### 问题：跨线程操作UI异常
**症状**：抛出"跨线程操作无效"异常
**解决方案**：
```csharp
// 安全的UI更新方法
private void SafeUpdateUI(Action action)
{
    if (InvokeRequired)
    {
        BeginInvoke(action);  // 异步调用，避免死锁
    }
    else
    {
        action();
    }
}

// 使用示例
private void OnDataReceived(string data)
{
    SafeUpdateUI(() => {
        dataLabel.Text = data;
        statusLabel.Text = "数据已更新";
    });
}
```

### 6.4 性能问题

#### 问题：UI响应缓慢
**原因**：在UI线程执行耗时操作
**解决方案**：
```csharp
// 使用异步方法处理耗时操作
private async void LoadDataAsync()
{
    try
    {
        // 显示加载状态
        SafeUpdateUI(() => loadingLabel.Visible = true);

        // 在后台线程执行耗时操作
        var data = await Task.Run(() => _service.LoadLargeDataSet());

        // 回到UI线程更新界面
        SafeUpdateUI(() => {
            dataGridView.DataSource = data;
            loadingLabel.Visible = false;
        });
    }
    catch (Exception ex)
    {
        SafeUpdateUI(() => {
            ShowError($"加载数据失败: {ex.Message}");
            loadingLabel.Visible = false;
        });
    }
}
```

## 7. 调试和测试指导

### 7.1 调试技巧

#### 7.1.1 模块加载调试
```csharp
// 在ModuleLoader中添加详细日志
private void LoadModulesFromAssembly(string assemblyPath)
{
    Logger.Debug($"开始加载程序集: {assemblyPath}");

    try
    {
        var assembly = Assembly.LoadFrom(assemblyPath);
        Logger.Debug($"程序集加载成功: {assembly.FullName}");

        var moduleTypes = assembly.GetTypes()
            .Where(t => typeof(IModule).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract)
            .ToList();

        Logger.Debug($"发现模块类型: {string.Join(", ", moduleTypes.Select(t => t.Name))}");

        // ... 继续处理
    }
    catch (Exception ex)
    {
        Logger.Error($"加载程序集失败: {assemblyPath}, 错误: {ex.Message}", ex);
    }
}
```

#### 7.1.2 事件调试
```csharp
// 添加事件发布日志
public void PublishEvent<T>(T eventData) where T : class
{
    try
    {
        Logger.Debug($"发布事件: {typeof(T).Name}, 数据: {JsonSerializer.Serialize(eventData)}");
        EventAggregator.GetEvent<PubSubEvent<T>>().Publish(eventData);
        Logger.Debug($"事件发布成功: {typeof(T).Name}");
    }
    catch (Exception ex)
    {
        Logger.Error($"事件发布失败: {typeof(T).Name}, 错误: {ex.Message}", ex);
    }
}
```

### 7.2 单元测试

#### 7.2.1 模块测试
```csharp
[TestClass]
public class DeviceModuleTests
{
    private Mock<IEventAggregator> _mockEventAggregator;
    private Mock<IDeviceService> _mockDeviceService;
    private DeviceModule _module;

    [TestInitialize]
    public void Setup()
    {
        _mockEventAggregator = new Mock<IEventAggregator>();
        _mockDeviceService = new Mock<IDeviceService>();

        _module = new DeviceModule
        {
            EventAggregator = _mockEventAggregator.Object,
            DeviceService = _mockDeviceService.Object
        };
    }

    [TestMethod]
    public void Initialize_ShouldSubscribeToEvents()
    {
        // Arrange
        var mockEvent = new Mock<DeviceConnectionEvent>();
        _mockEventAggregator.Setup(ea => ea.GetEvent<DeviceConnectionEvent>())
            .Returns(mockEvent.Object);

        // Act
        _module.Initialize();

        // Assert
        mockEvent.Verify(e => e.Subscribe(It.IsAny<Action<DeviceConnectionStatus>>(),
            ThreadOption.UIThread, false), Times.Once);
    }
}
```

#### 7.2.2 Presenter测试
```csharp
[TestClass]
public class DevicePresenterTests
{
    private Mock<IDeviceService> _mockDeviceService;
    private Mock<IEventAggregator> _mockEventAggregator;
    private Mock<IView> _mockView;
    private DevicePresenter _presenter;

    [TestInitialize]
    public void Setup()
    {
        _mockDeviceService = new Mock<IDeviceService>();
        _mockEventAggregator = new Mock<IEventAggregator>();
        _mockView = new Mock<IView>();

        _presenter = new DevicePresenter(_mockDeviceService.Object, _mockEventAggregator.Object)
        {
            View = _mockView.Object
        };
    }

    [TestMethod]
    public void LoadDeviceList_WhenServiceThrows_ShouldShowError()
    {
        // Arrange
        _mockDeviceService.Setup(s => s.GetDevices())
            .Throws(new Exception("服务错误"));

        // Act
        _presenter.LoadDeviceList();

        // Assert
        _mockView.Verify(v => v.ShowError(It.Is<string>(msg => msg.Contains("服务错误"))), Times.Once);
    }
}
```

## 8. 部署和维护

### 8.1 部署检查清单
- [ ] 所有必需的DLL文件已复制到Modules目录
- [ ] 配置文件格式正确
- [ ] 日志目录具有写权限
- [ ] 目标机器已安装.NET Framework 4.8
- [ ] 防火墙设置允许必要的网络通信

### 8.2 运行时监控
```csharp
// 添加性能计数器
public class PerformanceMonitor
{
    private readonly PerformanceCounter _cpuCounter;
    private readonly PerformanceCounter _memoryCounter;

    public void LogPerformanceMetrics()
    {
        var cpuUsage = _cpuCounter.NextValue();
        var memoryUsage = _memoryCounter.NextValue();

        Logger.Info($"CPU使用率: {cpuUsage:F2}%, 内存使用: {memoryUsage:F2}MB");

        if (cpuUsage > 80)
            Logger.Warn("CPU使用率过高");

        if (memoryUsage > 1024)
            Logger.Warn("内存使用量过高");
    }
}
```

### 8.3 故障恢复
```csharp
// 模块异常恢复机制
public void HandleModuleException(IModule module, Exception ex)
{
    Logger.Error($"模块 {module.Name} 发生异常: {ex.Message}", ex);

    try
    {
        // 尝试重启模块
        module.Stop();
        Thread.Sleep(1000);
        module.Start();

        Logger.Info($"模块 {module.Name} 重启成功");
    }
    catch (Exception restartEx)
    {
        Logger.Error($"模块 {module.Name} 重启失败: {restartEx.Message}", restartEx);

        // 禁用模块
        DisableModule(module);
    }
}
```

---

**文档版本**: v1.0
**创建日期**: 2024年12月19日
**适用架构**: WinForms DryIoc + EventAggregator 模块化架构
**维护团队**: 工业自动化开发团队
